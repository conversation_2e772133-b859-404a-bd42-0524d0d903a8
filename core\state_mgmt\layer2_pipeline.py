from typing import Dict, Any, List, Optional
import time
import uuid
import asyncio
from pydantic import ValidationError

from core.exceptions import PipelineError, Layer2Error
from core.logger import StructuredLogger
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.state_mgmt.states.Layer2 import Layer2, PipelineStep
from .mcp_mock import MockMCPAgent
# from .Layer2SchemaLoader import Layer2SchemaLoader

class Layer2Pipeline:
    """
    Executes a sequence of procedural steps (Layer 2) for a given state.
    """
    def __init__(self, layer2_config: Layer2, tools_registry: Dict[str, Any], memory_interface, state_id: str):
        self.state_id = state_id
        self.memory = memory_interface
        self.tools_registry = tools_registry
        self.layer2_config = layer2_config
        self.steps = self.layer2_config.pipeline
        self.logger = StructuredLogger(f"layer2_pipeline_{state_id}")
        if not self.steps:
            raise Layer2Error(f"Layer2 configuration for state '{state_id}' has no steps defined")
    
    async def run(self, input_data: dict, session_context: dict) -> StateOutput:
        """
        Executes the pipeline steps in order, calling MCP-compliant agents.

        Args:
            input_data (dict): Input data for the pipeline.
            session_context (dict): Session context (e.g., session_id, user_id).

        Returns:
            StateOutput: Standardized output with status, outputs, metrics, and logs.
        """
        start_time = time.time()
        logs = []
        metrics = {"duration_ms": 0}
        outputs = {}
        status = StatusType.SUCCESS
        current_input = input_data.copy()
        pipeline_trace_id = str(uuid.uuid4())
        session_id = session_context.get("session_id", "unknown")

        # Log pipeline execution start
        self.logger.log(
            level="info",
            message="Starting Layer2 pipeline execution",
            session_id=session_id,
            state_id=self.state_id,
            layer="layer2_pipeline",
            step="run",
            custom_module="Layer2Pipeline",
            agent_name=f"pipeline_{self.state_id}",
            action="run_pipeline",
            input_data=input_data,
            output_data={},
            status="processing",
            trace_id=pipeline_trace_id
        )

        try:
            for step_idx, step in enumerate(self.steps):
                step_start_time = time.time()
                step_name = step.step
                agent_name = step.agent
                step_trace_id = str(uuid.uuid4())

                # Log step execution start
                self.logger.log(
                    level="info",
                    message=f"Executing pipeline step: {step_name}",
                    session_id=session_id,
                    state_id=self.state_id,
                    layer="layer2_pipeline",
                    step=step_name,
                    custom_module="Layer2Pipeline",
                    agent_name=agent_name,
                    action="execute_step",
                    input_data=current_input,
                    output_data={},
                    status="processing",
                    trace_id=step_trace_id
                )
                

                
                # Get agent from tools_registry
                agent: MockMCPAgent = self.tools_registry.get(agent_name)
                if not agent:
                    raise PipelineError(f"Agent '{agent_name}' not found in tools registry")
                
                # Prepare step context
                step_context = {
                    **session_context,
                    "step_input": current_input,
                    "memory": self.memory
                }
                
                # Execute agent
                try:
                    agent_output = await agent.process(current_input, step_context)
                    outputs.update(agent_output)
                    
                    # Update current_input for next step
                    for output_key, output_ref in step.output.items():
                        if output_ref in outputs:
                            current_input[output_key] = outputs[output_ref]
                        else:
                            raise PipelineError(f"Output '{output_ref}' not found for step '{step_name}'")
                    
                    # Log step success
                    step_duration = (time.time() - step_start_time) * 1000
                    logs.append({
                        "step": step_name,
                        "latency": step_duration,
                        "status": "success",
                        "message": f"Completed step {step_name}"
                    })
                    metrics[f"step_{step_name}_ms"] = step_duration
                    
                except Exception as e:
                    # Handle errors based on onError configuration
                    if self.layer2_config.onError and self.layer2_config.onError.retry > 0:
                        # Retry logic (simplified)
                        for attempt in range(self.layer2_config.onError.retry):
                            try:
                                agent_output = await agent.process(current_input, step_context)
                                outputs.update(agent_output)
                                step_duration = (time.time() - step_start_time) * 1000
                                logs.append({
                                    "step": step_name,
                                    "latency": step_duration,
                                    "status": "success",
                                    "message": f"Completed step {step_name} after retry"
                                })
                                metrics[f"step_{step_name}_ms"] = step_duration
                                break
                            except Exception as retry_e:
                                pass  # Continue to next retry
                        else:
                            status = StatusType.FAILURE
                            error_msg = f"Step '{step_name}' failed after retries: {str(e)}"
                            logs.append({"step": step_name, "status": "failure", "message": error_msg})
                            raise PipelineError(error_msg)
                    else:
                        status = StatusType.FAILURE
                        error_msg = f"Step '{step_name}' failed: {str(e)}"
                        logs.append({"step": step_name, "status": "failure", "message": error_msg})
                        raise PipelineError(error_msg)
                
                # Check for interrupts
                if self.layer2_config.onInterrupt and self._check_interrupt(session_context):
                    status = StatusType.FAILURE
                    error_msg = f"Pipeline interrupted at step '{step_name}'"
                    logs.append({"step": step_name, "status": "interrupt", "message": error_msg})
                    outputs["handler"] = self.layer2_config.onInterrupt.handler
                    outputs["resume_from"] = self.layer2_config.onInterrupt.resume_from
                    break
            
        except PipelineError as e:
            status = StatusType.FAILURE
            outputs["error"] = str(e)
        except Exception as e:
            status = StatusType.FAILURE
            outputs["error"] = f"Unexpected error: {str(e)}"
        
        # Calculate total duration
        metrics["duration_ms"] = (time.time() - start_time) * 1000
        
        return StateOutput(
            status=status,
            message="Pipeline execution completed" if status == StatusType.SUCCESS else "Pipeline execution failed",
            code=StatusCode.OK if status == StatusType.SUCCESS else StatusCode.ERROR,
            outputs=outputs,
            meta={"logs": logs, "metrics": metrics}
        )
    
    def _check_interrupt(self, session_context: dict) -> bool:
        """
        Checks for interrupt conditions (placeholder for actual interrupt logic).
        """
        # TODO: Implement actual interrupt detection (e.g., check session_context for interrupt flag)
        return False