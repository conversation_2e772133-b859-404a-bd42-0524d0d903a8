import sys
import asyncio
import os
import time
from dotenv import load_dotenv
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from state_mgmt.StateManager import StateManager
from asteval import Interpreter

# Load variables from .env file into environment
load_dotenv()

def test_state_output_success():
    return StateOutput(
        status=StatusType.SUCCESS,
        message="Operation successful",
        code=StatusCode.OK,
        outputs={"data": "test"},
        meta={"source": "test_suite"}
    )

async def run_trial():
    """
    Runs a simple trial of the StateManager with the GenericBank workflow.
    """
    sm = StateManager("GenericBank.json", "test_session", "user_1")
    
    # Step 1: Simulate greeting state
    print("\n=== Step 1: Greeting State ===")
    input_data = {"intent": "check_balance", "slots": {"account_id": "12345"}}
    result = await sm.execute_step(input_data)
    print("State Output:", result)
    print("Current State ID:", sm.current_state_id)
    
    # Step 2: Simulate check balance state
    print("\n=== Step 2: Check Balance State ===")
    input_data = {"account_id": "12345"}
    result = await sm.execute_step(input_data)
    print("State Output:", result)
    print("Current State ID:", sm.current_state_id)
    
    # Step 3: Simulate goodbye state
    print("\n=== Step 3: Goodbye State ===")
    input_data = {}
    result = await sm.execute_step(input_data)
    print("State Output:", result)
    print("Current State ID:", sm.current_state_id)

if __name__ == "__main__":

    asyncio.run(run_trial())

    # sm = StateManager("GenericBank.json", "test_session","user_1")

    # state = sm.get_state("state_greeting")

    # print(sm.example_memory_usage())
    
    # --- Turn 1 ---
    # user_input_1 = "Hello, what's my balance?"
    # ai_response_1 = "Sure, I can check your balance. What's your account ID?"
    # sm.memory_manager.add_conversation_turn(user_input_1, ai_response_1, intent="greeting")
    # print(f"User: {user_input_1}")
    # print(f"AI: {ai_response_1}")
    # # This line shows what's currently in memory for contextual:
    # print("Current Contextual Memory after Turn 1:", sm.memory_manager.get_all_contextual())

    # time.sleep(1) # Simulate a 1-second pause

    # # --- Turn 2 ---
    # user_input_2 = "My account ID is 12345."
    # ai_response_2 = "Thanks! Your balance is $5000."
    # sm.memory_manager.add_conversation_turn(user_input_2, ai_response_2, intent="provide_account_id")
    # print(f"User: {user_input_2}")
    # print(f"AI: {ai_response_2}")
    # # This line shows what's currently in memory for contextual after the second turn:
    # print("Current Contextual Memory after Turn 2:", sm.memory_manager.get_all_contextual())

    # # This line saves the final in-memory contextual data to the JSON file:
    # sm.memory_manager.contextual.save_to_file("contextual_memory_log.json")
    # print("Final Contextual Memory saved to contextual_memory_log.json")

    # time.sleep(4)
    # sm.handle_intent("goodbye")
    # sm.memory_manager.set("persistent", "test", "test3")
    # sm.memory_manager.set("persistent", "test1", "test1")
    # print("Current Persistent Memory:", sm.memory_manager.get_all_persistent())
    # print("Loaded State:", state.id)
    # print(state)

    # context = {"intent" : "check_balance", "account_id": "12345"}
    # aeval = state.transitions[0].createAeval(context)

    # print(state.transitions[0])
    # print(state.transitions[0].evaluate(aeval))

    # layer2 = sm.get_layer2("l2_check_balance")
    # print("Loaded Layer2:", layer2.id)
    # print(layer2)

    # print(test_state_output_success())

    # # --- Execute the entire workflow step by step ---
    # print("\n--- Executing Workflow ---")
    # sm.is_running = True
    # step_count = 0
    # while sm.is_running:
    #     print(f"\nStep {step_count} (Current State: {sm.current_state_id})")
    #     output = sm.execute_step({})
    #     print("Step Output:", output)
    #     print(f"Next State: {sm.current_state_id}")
    #     step_count += 1
    # print("\nWorkflow execution complete.")





