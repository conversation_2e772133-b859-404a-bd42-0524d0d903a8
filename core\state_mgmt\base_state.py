from typing import Dict, Any, Optional
import time
import asyncio
from pydantic import ValidationError

from core.exceptions import WorkflowError, StateTransitionError
from core.logger import StructuredLogger
from core.state_mgmt.memory_manager import MemoryManager
from core.state_mgmt.states.Layer2 import Layer2
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from core.state_mgmt.states.Workflow import State as StateConfig
from .layer2_pipeline import Layer2Pipeline
from core.state_mgmt.states.transition import Transition
from asteval import Interpreter

class State:
    """
    Represents a workflow state (Layer 1) that orchestrates execution and transitions.
    """
    def __init__(self, state_id: str, config: StateConfig, layer2_config: Layer2, memory: MemoryManager, tools_registry: Dict[str, Any]):
        self.id = state_id
        self.config = config
        self.layer2_config = layer2_config
        self.memory = memory
        self.tools_registry = tools_registry
        self.logger = StructuredLogger(f"state_{state_id}")
        
    async def execute(self, input_data: dict, session_context: dict) -> StateOutput:
        """
        Executes the state by running its Layer 2 pipeline and evaluating transitions.
        
        Args:
            input_data (dict): Input data for the state (e.g., user input).
            session_context (dict): Session context (e.g., session_id, user_id).
        
        Returns:
            StateOutput: Standardized output with status, outputs, metrics, logs, and next state.
        """
        start_time = time.time()
        logs = []
        metrics = {"duration_ms": 0}
        outputs = {}
        status = StatusType.SUCCESS
        next_state_id = None

        try:
            # Validate input data against expected_input
            for key in self.config.expected_input:
                if key not in input_data:
                    raise WorkflowError(f"Missing expected input '{key}' for state '{self.id}'")
            
            self.logger.info("Executing state", {
                "state_id": self.id,
                "input_data": input_data,
                "session_context": session_context
            })
            
            # Fetch additional context from memory
            memory_context = self.memory.get_all_contextual()
            combined_context = {**session_context, **memory_context}
            
            # Initialize Layer2Pipeline
            pipeline = Layer2Pipeline(
                layer2_config=self.layer2_config,
                tools_registry=self.tools_registry,
                memory_interface=self.memory,
                state_id=self.id
            )
            
            # Execute pipeline
            pipeline_output = await pipeline.run(input_data, combined_context)
            logs.extend(pipeline_output.meta["logs"])

            outputs.update(pipeline_output.outputs)
            metrics.update(pipeline_output.meta["metrics"])
            
            if pipeline_output.status != StatusType.SUCCESS:
                status = pipeline_output.status
                self.logger.error("Pipeline execution failed", {
                    "state_id": self.id,
                    "pipeline_status": pipeline_output.status,
                    "pipeline_message": pipeline_output.message
                })
                raise WorkflowError(f"Pipeline failed: {pipeline_output.message}")
            
            # Validate outputs against expected_output
            for key in self.config.expected_output:
                if key not in outputs:
                    raise WorkflowError(f"Missing expected output '{key}' for state '{self.id}'")
            
            # Evaluate transitions
            aeval = Interpreter()
            for key, value in combined_context.items():
                aeval.symtable[key] = value
            for output_key, output_value in outputs.items():
                aeval.symtable[output_key] = output_value
            
            for transition in self.config.transitions:
                if transition.evaluate(aeval):
                    next_state_id = transition.target
                    self.logger.info("Transition condition met", {
                        "state_id": self.id,
                        "condition": transition.condition,
                        "next_state": next_state_id
                    })
                    break
            else:
                self.logger.warning("No transition conditions met", {"state_id": self.id})
            
            # Store outputs in memory if needed
            self.memory.set("contextual", f"state_{self.id}_outputs", outputs)
            
        except WorkflowError as e:
            status = StatusType.ERROR
            self.logger.error("State execution failed", {
                "state_id": self.id,
                "error": str(e)
            })
            outputs["error"] = str(e)
            logs.append({"step": "state_execution", "status": "failure", "message": str(e)})
        except StateTransitionError as e:
            status = StatusType.ERROR
            self.logger.error("Transition evaluation failed", {
                "state_id": self.id,
                "error": str(e)
            })
            outputs["error"] = str(e)
            logs.append({"step": "transition_evaluation", "status": "failure", "message": str(e)})
        except Exception as e:
            status = StatusType.ERROR
            self.logger.critical("Unexpected error in state execution", {
                "state_id": self.id,
                "error": str(e)
            })
            outputs["error"] = f"Unexpected error: {str(e)}"
            logs.append({"step": "state_execution", "status": "failure", "message": str(e)})
        
        # Calculate total duration
        metrics["duration_ms"] = (time.time() - start_time) * 1000
        
        return StateOutput(
            status=status,
            message="State execution completed" if status == StatusType.SUCCESS else "State execution failed",
            code=StatusCode.OK if status == StatusType.SUCCESS else StatusCode.INTERNAL_ERROR,
            outputs=outputs,
            meta={"logs": logs, "metrics": metrics, "next": next_state_id}
        )