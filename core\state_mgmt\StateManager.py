import asyncio
import os
import json
import time
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone

from core.exceptions import Configuration<PERSON>rror, WorkflowError, Layer2Error
from core.logger import StructuredLogger

from .WorkflowSchemaLoader import WorkflowSchemaLoader
from .Layer2SchemaLoader import Layer2SchemaLoader
from .states.Workflow import WorkflowWrapper,State
from .states.Layer2 import Layer2, PipelineStep
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from asteval import Interpreter
from .memory_manager import MemoryManager
from .base_state import State
from .mcp_mock import MockMCPAgent

# Add config loader
def load_config():
    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    config_path = os.path.join(base_dir, "config", "state_manager_config.json")
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        # Use a temporary logger for config loading errors
        temp_logger = StructuredLogger("config_loader")
        temp_logger.log(
            level="error",
            message="Failed to load state manager configuration",
            session_id="system",
            state_id="config_loading",
            layer="system",
            step="load_config",
            custom_module="StateManager",
            agent_name="config_loader",
            action="load_config",
            input_data={"config_path": config_path},
            output_data={"error": str(e)},
            status="error",
            reason=f"Configuration file error: {e}"
        )
        raise ConfigurationError(f"Failed to load state manager configuration: {e}")



class StateManager:
    """
    Main State Manager - the brain of the agent's execution loop.
    
    This class manages:
    - Workflow state transitions based on the provided schema format
    - Execution of Layer2 pipelines
    - Memory management 
    - Agent coordination (TODO: integrate with MCPAgentRegistry)
    """
    
    def __init__(self, workflow_name: str, session_id: str, user_id: Optional[str] = None):
        """
        Initialize the State Manager.
        
        Args:
            workflow_name: Name of the workflow to execute
            session_id: Unique session identifier
            user_id: Optional user identifier
            
        Raises:
            ConfigurationError: If configuration files cannot be loaded
            WorkflowError: If the workflow cannot be loaded or is invalid
            Layer2Error: If a Layer2 definition cannot be loaded or is invalid
        """
        self.workflow_name = workflow_name
        self.user_id = user_id or f"anon_{session_id}"
        self.session_id = session_id
        self.trace_id = str(uuid.uuid4())

        # Initialize logger first
        self.logger = StructuredLogger(f"state_manager_{session_id}")

        # Log initialization start
        self.logger.log(
            level="info",
            message="Initializing StateManager",
            session_id=session_id,
            state_id="initialization",
            layer="state_manager",
            step="init",
            custom_module="StateManager",
            agent_name="state_manager",
            action="initialize",
            input_data={
                "workflow_name": workflow_name,
                "session_id": session_id,
                "user_id": self.user_id
            },
            output_data={},
            status="processing",
            trace_id=self.trace_id
        )

        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        # Use config for path management
        try:
            self.config = load_config()
            workflow_states_dir = self.config["paths"]["workflow_states_dir"]
            self.workflow_file_path = os.path.join(base_dir, workflow_states_dir, workflow_name)

            self.logger.log(
                level="info",
                message="Configuration loaded successfully",
                session_id=session_id,
                state_id="initialization",
                layer="state_manager",
                step="load_config",
                custom_module="StateManager",
                agent_name="state_manager",
                action="load_config",
                input_data={"workflow_name": workflow_name},
                output_data={"config_loaded": True, "workflow_path": self.workflow_file_path},
                status="success",
                trace_id=self.trace_id
            )
        except ConfigurationError as e:
            self.logger.log(
                level="error",
                message="Configuration loading failed",
                session_id=session_id,
                state_id="initialization",
                layer="state_manager",
                step="load_config",
                custom_module="StateManager",
                agent_name="state_manager",
                action="load_config",
                input_data={"workflow_name": workflow_name},
                output_data={"error": str(e)},
                status="error",
                reason=f"Configuration error: {e}",
                trace_id=self.trace_id
            )
            raise
        
        self.workflow: WorkflowWrapper = None
        self.layer2_map: Dict[str, Layer2] = {}

        # MEMORY MANAGER INSTANTIATION
        self.memory_manager = MemoryManager(session_id, user_id)
        self.execution_history = []
        self.is_running = True
        self.current_state_id = None

        # TODO: Add MCPAgentRegistry integration
        # self.agent_registry = MCPAgentRegistry()
        

        # Load workflow and Layer2 schemas
        try:
            self._load_workflow()
            self._load_all_layer2()
            # Set initial state
            if self.workflow and hasattr(self.workflow.workflow, 'start'):
                self.current_state_id = self.workflow.workflow.start

            self.logger.log(
                level="info",
                message="StateManager initialization completed successfully",
                session_id=session_id,
                state_id="initialization",
                layer="state_manager",
                step="complete_init",
                custom_module="StateManager",
                agent_name="state_manager",
                action="initialize",
                input_data={"workflow_name": workflow_name},
                output_data={
                    "workflow_loaded": self.workflow is not None,
                    "initial_state": self.current_state_id,
                    "layer2_count": len(self.layer2_map)
                },
                status="success",
                trace_id=self.trace_id
            )
        except Exception as e:
            self.logger.log(
                level="error",
                message="StateManager initialization failed",
                session_id=session_id,
                state_id="initialization",
                layer="state_manager",
                step="complete_init",
                custom_module="StateManager",
                agent_name="state_manager",
                action="initialize",
                input_data={"workflow_name": workflow_name},
                output_data={"error": str(e)},
                status="error",
                reason=f"Initialization error: {e}",
                trace_id=self.trace_id
            )
            self.workflow = None

        self.current_state_id: str = self.workflow.workflow.start
        self.current_layer2_id_step: str = self.layer2_map.get(self.get_state(self.current_state_id).layer2_id).pipeline[0].step
    
    def _load_workflow(self):
        self.logger.log(
            level="info",
            message="Loading workflow schema",
            session_id=self.session_id,
            state_id="workflow_loading",
            layer="state_manager",
            step="load_workflow",
            custom_module="StateManager",
            agent_name="state_manager",
            action="load_workflow",
            input_data={"workflow_path": self.workflow_file_path},
            output_data={},
            status="processing",
            trace_id=self.trace_id
        )

        try:
            self.workflow = WorkflowSchemaLoader.load(self.workflow_file_path)
            if not self.workflow:
                raise Exception(f"Failed to load workflow: {self.workflow_file_path}")

            self.logger.log(
                level="info",
                message="Workflow schema loaded successfully",
                session_id=self.session_id,
                state_id="workflow_loading",
                layer="state_manager",
                step="load_workflow",
                custom_module="StateManager",
                agent_name="state_manager",
                action="load_workflow",
                input_data={"workflow_path": self.workflow_file_path},
                output_data={
                    "workflow_id": self.workflow.workflow.id if self.workflow else None,
                    "states_count": len(self.workflow.workflow.states) if self.workflow else 0
                },
                status="success",
                trace_id=self.trace_id
            )
        except Exception as e:
            self.logger.log(
                level="error",
                message="Failed to load workflow schema",
                session_id=self.session_id,
                state_id="workflow_loading",
                layer="state_manager",
                step="load_workflow",
                custom_module="StateManager",
                agent_name="state_manager",
                action="load_workflow",
                input_data={"workflow_path": self.workflow_file_path},
                output_data={"error": str(e)},
                status="error",
                reason=f"Workflow loading error: {e}",
                trace_id=self.trace_id
            )
            self.workflow = None

    def _load_all_layer2(self):
        if not self.workflow or not hasattr(self.workflow, 'workflow'):
            print("No workflow loaded, cannot load Layer2 schemas.")
            return
        for state in self.workflow.workflow.states.values():
            layer2_id = getattr(state, 'layer2_id', None)
            if not layer2_id:
                continue
            # Get the absolute path using config
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            layer2_config_dir = self.config["paths"]["layer2_config_dir"]
            layer2_file = os.path.join(base_dir, layer2_config_dir, f"{layer2_id}.json")
            if not os.path.isfile(layer2_file):
                print(f"⚠️ Warning: Layer2 file not found - {layer2_file}")
                raise Exception(f"Workflow contains references to non-existent Layer2 definitions {layer2_id}")
            try:
                layer2_obj = Layer2SchemaLoader.load(layer2_file, state.expected_output)
                if layer2_obj:
                    self.layer2_map[layer2_id] = layer2_obj
                else:
                    print(f"⚠️ Warning: Failed to load Layer2 from {layer2_file}")
            except Exception as e:
                print(f"Error loading Layer2 {layer2_id}: {e}")

    def get_state(self, state_id: str):
        return self.workflow.workflow.states.get(state_id)

    def get_layer2(self, layer2_id: str):
        return self.layer2_map.get(layer2_id)

    # def execute_step(self, input_data: dict) -> StateOutput:
    #     state_config = self.get_state(self.current_state_id)
    #     state = State(
    #         state_id=self.current_state_id,
    #         config=state_config,
    #         layer2_config=self.get_layer2(state_config.layer2_id),
    #         memory_interface=self.memory_manager,
    #         tools_registry={
    #             "STT": MockMCPAgent("STT"),
    #             "IntentDetector": MockMCPAgent("IntentDetector"),
    #             "BalanceQuery": MockMCPAgent("BalanceQuery"),
    #             "TTS": MockMCPAgent("TTS")
    #         }
    #     )
    #     result = asyncio.run(state.execute(input_data, {
    #         "session_id": self.session_id,
    #         "user_id": self.user_id
    #     }))
    #     self.current_state_id = result.meta.get("next", self.current_state_id)
    #     return result

    async def execute_step(self, input_data: dict) -> StateOutput:
        """
        Executes a single step of the workflow using the current state.
        """
        step_start_time = time.time()
        step_trace_id = str(uuid.uuid4())

        self.logger.log(
            level="info",
            message="Starting workflow step execution",
            session_id=self.session_id,
            state_id=self.current_state_id,
            layer="state_manager",
            step="execute_step",
            custom_module="StateManager",
            agent_name="state_manager",
            action="execute_step",
            input_data=input_data,
            output_data={},
            status="processing",
            trace_id=step_trace_id
        )

        try:
            state_config = self.get_state(self.current_state_id)
            layer2_config = self.get_layer2(state_config.layer2_id)

            # Log state transition
            self.logger.log(
                level="info",
                message="Executing state with Layer2 pipeline",
                session_id=self.session_id,
                state_id=self.current_state_id,
                layer="state_manager",
                step="state_execution",
                custom_module="StateManager",
                agent_name="state_manager",
                action="execute_state",
                input_data={
                    "state_id": self.current_state_id,
                    "layer2_id": state_config.layer2_id,
                    "input_data": input_data
                },
                output_data={},
                status="processing",
                trace_id=step_trace_id
            )

            state = State(
                state_id=self.current_state_id,
                config=state_config,
                layer2_config=layer2_config,
                memory=self.memory_manager,
                tools_registry={
                    "stt_google": MockMCPAgent("stt_google"),
                    "intent_classifier": MockMCPAgent("intent_classifier"),
                    "llm_ack_gen": MockMCPAgent("llm_ack_gen"),
                    "tts_google": MockMCPAgent("tts_google"),
                    "validate_account_id": MockMCPAgent("validate_account_id"),
                    "balance_fetcher": MockMCPAgent("balance_fetcher"),
                    "llm_response_gen": MockMCPAgent("llm_response_gen"),
                    "llm_farewell_gen": MockMCPAgent("llm_farewell_gen")
                }
            )

            result = await state.execute(input_data, {
                "session_id": self.session_id,
                "user_id": self.user_id,
                "account_id": "12345"
            })

            # Update state and log transition
            previous_state = self.current_state_id
            self.current_state_id = result.meta.get("next", self.current_state_id)
            self.execution_history.append(result)

            step_duration = (time.time() - step_start_time) * 1000

            self.logger.log_state_output(
                level="info",
                message="Workflow step completed successfully",
                session_id=self.session_id,
                state_id=previous_state,
                layer="state_manager",
                step="execute_step",
                custom_module="StateManager",
                agent_name="state_manager",
                action="execute_step",
                state_output=result,
                input_data=input_data,
                metrics={"duration_ms": step_duration},
                trace_id=step_trace_id,
                next_state_id=self.current_state_id
            )

            return result

        except Exception as e:
            step_duration = (time.time() - step_start_time) * 1000

            self.logger.log(
                level="error",
                message="Workflow step execution failed",
                session_id=self.session_id,
                state_id=self.current_state_id,
                layer="state_manager",
                step="execute_step",
                custom_module="StateManager",
                agent_name="state_manager",
                action="execute_step",
                input_data=input_data,
                output_data={"error": str(e)},
                status="error",
                metrics={"duration_ms": step_duration},
                reason=f"Step execution error: {e}",
                trace_id=step_trace_id
            )
            raise

    def example_memory_usage(self):
        """
        Example of how to use the memory manager for all memory layers in StateManager.
        """
        # --- Ephemeral memory: store temporary pipeline data ---
        self.memory_manager.set("ephemeral", "transcribed_text", "Check my balance")
        # Retrieve ephemeral data
        transcribed = self.memory_manager.get("transcribed_text")
        # Clear ephemeral after pipeline
        self.memory_manager.clear_ephemeral()

        # --- Contextual memory: store session data and conversation ---
        self.memory_manager.set("contextual", "user_message", "Check my balance")
        self.memory_manager.set("contextual", "intent", "check_balance")
        self.memory_manager.set("contextual", "slots", {"account_id": "12345"})
        # Store conversation turns
        conversation = self.memory_manager.get("conversation") or []
        conversation.append({"role": "user", "text": "Check my balance 2"})
        conversation.append({"role": "ai", "text": "Sure, I'll check your balance 2"})
        self.memory_manager.set("contextual", "conversation", conversation)
        # Save contextual memory to file for logging/debugging
        self.memory_manager.contextual.save_to_file("contextual_memory_log.json")
        conversation.append({"role": "user", "text": "Check my balance 1"})
        conversation.append({"role": "ai", "text": "Sure, I'll check your balance 1"})
        self.memory_manager.set("contextual", "conversation", conversation)
        # Clear contextual at end of session
        # self.memory_manager.clear_contextual()
        print(self.memory_manager.contextual.get("conversation"))
        print(self.memory_manager.contextual.get_all())

        # --- Persistent memory: store long-term user data ---
        self.memory_manager.set("persistent", "validated_account_id", "12345")
        self.memory_manager.set("persistent", "account_balance_history", {"2025-06-17": 5000})
        # Retrieve persistent data
        balance_history = self.memory_manager.get("account_balance_history")
        # Explicit memory saving (e.g., user says "Remember my preferred language is English")
        self.memory_manager.explicit_save("save_preference", {"preference": "language", "value": "en"})
        # Retrieve preference
        language = self.memory_manager.get("language")

    def end_session_cleanup(self):
        """Clear all session and ephemeral memory at the end of a conversation."""
        cleanup_trace_id = str(uuid.uuid4())

        self.logger.log(
            level="info",
            message="Starting session cleanup",
            session_id=self.session_id,
            state_id=self.current_state_id or "session_end",
            layer="state_manager",
            step="cleanup",
            custom_module="StateManager",
            agent_name="state_manager",
            action="end_session_cleanup",
            input_data={"session_id": self.session_id},
            output_data={},
            status="processing",
            trace_id=cleanup_trace_id
        )

        try:
            # Save session data before clearing
            contextual_data = self.memory_manager.get_all_contextual()

            self.memory_manager.clear_contextual()
            self.memory_manager.clear_ephemeral()

            self.logger.log(
                level="info",
                message="Session cleanup completed successfully",
                session_id=self.session_id,
                state_id=self.current_state_id or "session_end",
                layer="state_manager",
                step="cleanup",
                custom_module="StateManager",
                agent_name="state_manager",
                action="end_session_cleanup",
                input_data={"session_id": self.session_id},
                output_data={
                    "contextual_data_saved": len(contextual_data),
                    "cleanup_completed": True
                },
                status="success",
                trace_id=cleanup_trace_id
            )
        except Exception as e:
            self.logger.log(
                level="error",
                message="Session cleanup failed",
                session_id=self.session_id,
                state_id=self.current_state_id or "session_end",
                layer="state_manager",
                step="cleanup",
                custom_module="StateManager",
                agent_name="state_manager",
                action="end_session_cleanup",
                input_data={"session_id": self.session_id},
                output_data={"error": str(e)},
                status="error",
                reason=f"Cleanup error: {e}",
                trace_id=cleanup_trace_id
            )

    def handle_intent(self, intent):
        """Example handler to show when to clear memory."""
        self.logger.log(
            level="info",
            message="Handling intent",
            session_id=self.session_id,
            state_id=self.current_state_id or "intent_handling",
            layer="state_manager",
            step="handle_intent",
            custom_module="StateManager",
            agent_name="state_manager",
            action="handle_intent",
            input_data={"intent": intent},
            output_data={},
            status="processing"
        )

        if intent == "goodbye":
            self.end_session_cleanup()
            # Transition to end state, etc.
