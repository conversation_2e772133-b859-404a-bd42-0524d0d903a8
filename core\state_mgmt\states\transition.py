from asteval import Interpreter
from pydantic import BaseModel


class Transition(BaseModel):
    condition: str
    target: str
    # def __init__(self, condition: str, target: str):
    #     self.condition = condition
    #     self.target = target

    def __repr__(self):
        return f"Transition(condition={self.condition}, target={self.target})"
    
    def evaluate(self, aeval: Interpreter) -> bool:
        """
        Evaluates the transition condition against the provided context.
        
        Args:
            context (dict): The context in which to evaluate the condition.
        
        Returns:
            bool: True if the condition is met, False otherwise.
        """
        try:

            
            if self.condition == "true":
                shouldAdvance = True
            else:
                shouldAdvance = aeval(self.condition) 
            return shouldAdvance == True
        except Exception as e:
            print(f"Error evaluating condition '{self.condition}': {e}")
            return False
    

    def createAeval(self, context: dict) -> Interpreter:
        """
        Creates an Asteval interpreter with the provided context.
        
        Args:
            context (dict): The context to be used in the Asteval interpreter.
        
        Returns:
            Interpreter: An instance of Asteval interpreter with the context.
        """
        aeval = Interpreter()
        for key, value in context.items():
            aeval.symtable[key] = value
        return aeval